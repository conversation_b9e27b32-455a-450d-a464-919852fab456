# 代理IP轮换机制集成指南

## 📋 概述

本文档介绍了如何在Amazon爬虫脚本中集成代理IP轮换机制，以解决多变体产品价格获取时的HTTP请求频繁限制问题。

## 🎯 主要功能

### 1. 代理轮换管理器 (ProxyRotator)
- **自动代理轮换**: 为每个HTTP请求自动选择不同的代理IP
- **失败检测**: 自动检测代理失败并标记为不可用
- **智能重试**: 代理失败时自动切换到下一个可用代理
- **统计监控**: 提供代理使用统计信息

### 2. 集成点
- `_fetch_variants_price_bulk`: 批量获取变体价格时使用代理轮换
- `_fetch_single_price`: 单个价格获取时使用代理轮换
- `_make_request_with_proxy_rotation`: 统一的代理请求方法

## 📁 文件结构

```
项目目录/
├── amazon_scraper.py          # 主爬虫脚本（已集成代理轮换）
├── 代理IP.txt                 # 代理IP列表文件
├── test_proxy_rotation.py     # 代理轮换测试脚本
└── PROXY_ROTATION_GUIDE.md    # 本使用指南
```

## 🔧 配置说明

### 1. 代理IP文件格式 (`代理IP.txt`)

```
IP:PORT
**************:9577
************:6047
*************:5616
...
```

**注意**: 
- 每行一个代理，格式为 `IP:PORT`
- 用户名和密码在代码中统一配置
- 默认用户名: `xinyc1126`
- 默认密码: `woaiwojia001`

### 2. 代理轮换器配置

```python
proxy_rotator = ProxyRotator(
    proxy_file="代理IP.txt",      # 代理文件路径
    username="xinyc1126",         # 代理用户名
    password="woaiwojia001",      # 代理密码
    verbose=True                  # 是否显示详细日志
)
```

## 🚀 使用方法

### 1. API调用方式

通过Flask API调用时，代理轮换会自动启用（当`enable_variants=true`时）：

```bash
curl -X POST http://localhost:5000/extract \
  -H "Content-Type: application/json" \
  -d '{
    "html_content": "...",
    "enable_variants": true,
    "warehouse_id": "3079",
    "send_to_api": true
  }'
```

### 2. 直接使用代理轮换器

```python
from amazon_scraper import ProxyRotator

# 创建代理轮换器
rotator = ProxyRotator()

# 获取代理
proxy = rotator.get_next_proxy()

# 使用代理发送请求
response = requests.get(url, proxies=proxy)

# 如果代理失败，标记并获取下一个
if response.status_code in [403, 429, 503]:
    rotator.mark_proxy_failed(proxy)
    next_proxy = rotator.get_next_proxy()
```

### 3. 集成到现有代码

```python
# 创建带代理轮换的爬虫实例
scraper = AmazonProductScraper(
    html_content=html_content,
    proxy_rotator=proxy_rotator,  # 传入代理轮换器
    enable_variants=True
)
```

## 🧪 测试验证

### 1. 运行代理测试脚本

```bash
python test_proxy_rotation.py
```

测试内容包括：
- 代理轮换器基本功能测试
- 代理连接性测试
- Amazon访问测试

### 2. 测试输出示例

```
🧪 代理轮换器功能测试
========================================
✅ 成功加载 250 个代理IP
🔄 切换到代理: **************:9577
✅ 代理测试成功! 响应时间: 1.23秒
📊 当前统计: 可用=249, 失败=1
```

## 📊 监控和统计

### 1. 获取代理统计信息

```python
stats = proxy_rotator.get_proxy_stats()
print(f"总代理数: {stats['total_proxies']}")
print(f"可用代理数: {stats['available_proxies']}")
print(f"失败代理数: {stats['failed_proxies']}")
print(f"当前代理: {stats['current_proxy']}")
```

### 2. 日志输出

启用详细日志时，会显示：
- 代理切换信息
- 请求成功/失败状态
- 代理标记失败的原因
- 实时统计信息

## ⚠️ 注意事项

### 1. 代理质量
- 确保代理IP列表中的代理可用且稳定
- 定期更新代理列表
- 监控代理成功率

### 2. 请求频率
- 即使使用代理轮换，也要控制请求频率
- 建议在请求间添加适当延迟
- 避免同时发送大量请求

### 3. 错误处理
- 代理轮换器会自动处理代理失败
- 当所有代理都失败时，会重新加载代理列表
- 建议监控代理池状态

### 4. 性能考虑
- 代理请求比直连请求慢
- 批量请求时要考虑超时设置
- 合理设置重试次数

## 🔍 故障排除

### 1. 常见问题

**问题**: 代理文件加载失败
```
❌ 代理文件不存在: 代理IP.txt
```
**解决**: 确保`代理IP.txt`文件存在且格式正确

**问题**: 所有代理都失败
```
❌ 没有可用代理
```
**解决**: 检查代理质量，更新代理列表

**问题**: 请求超时
```
❌ 请求异常: timeout
```
**解决**: 增加超时时间或更换更快的代理

### 2. 调试方法

1. 启用详细日志: `verbose=True`
2. 运行测试脚本验证代理
3. 检查代理统计信息
4. 监控请求成功率

## 📈 性能优化建议

### 1. 代理池管理
- 定期测试代理可用性
- 移除长期失败的代理
- 添加新的高质量代理

### 2. 请求优化
- 使用连接池复用连接
- 合理设置超时时间
- 实现智能重试机制

### 3. 监控告警
- 监控代理成功率
- 设置代理池数量告警
- 记录请求失败日志

## 🎉 总结

代理IP轮换机制的集成显著提高了多变体产品价格获取的成功率和稳定性，通过自动轮换代理IP有效避免了单个IP被封禁的问题。配合合理的请求频率控制和错误处理机制，可以大幅提升爬虫的可靠性。
