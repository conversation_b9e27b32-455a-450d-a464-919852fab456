#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
环境配置检查脚本
检查宝塔面板Python项目环境是否正确配置代理轮换功能
"""

import os
import sys
import importlib
import requests
import json

def check_python_version():
    """检查Python版本"""
    print("=" * 60)
    print("🐍 Python环境检查")
    print("=" * 60)
    
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("✅ Python版本符合要求 (>=3.7)")
        return True
    else:
        print("❌ Python版本过低，建议使用Python 3.7+")
        return False

def check_required_modules():
    """检查必需的Python模块"""
    print("\n" + "=" * 60)
    print("📦 Python模块检查")
    print("=" * 60)
    
    required_modules = [
        'requests',
        'flask', 
        'bs4',
        'json',
        'threading',
        'collections',
        'concurrent.futures'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - 未安装")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ 缺少模块: {', '.join(missing_modules)}")
        print("请在宝塔面板中安装缺少的模块")
        return False
    else:
        print("\n✅ 所有必需模块已安装")
        return True

def check_project_files():
    """检查项目文件"""
    print("\n" + "=" * 60)
    print("📁 项目文件检查")
    print("=" * 60)
    
    required_files = [
        'amazon_scraper.py',
        '代理IP.txt'
    ]
    
    optional_files = [
        'test_api_proxy.py',
        'proxy_manager.py',
        'example_usage.py',
        'PROXY_ROTATION_GUIDE.md'
    ]
    
    missing_files = []
    
    print("必需文件:")
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} ({size:,} bytes)")
        else:
            print(f"❌ {file} - 文件不存在")
            missing_files.append(file)
    
    print("\n可选文件:")
    for file in optional_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} ({size:,} bytes)")
        else:
            print(f"⚠️ {file} - 文件不存在")
    
    if missing_files:
        print(f"\n❌ 缺少必需文件: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ 所有必需文件存在")
        return True

def check_proxy_file():
    """检查代理文件配置"""
    print("\n" + "=" * 60)
    print("🌐 代理配置检查")
    print("=" * 60)
    
    proxy_file = "代理IP.txt"
    
    if not os.path.exists(proxy_file):
        print(f"❌ 代理文件不存在: {proxy_file}")
        return False
    
    try:
        with open(proxy_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        valid_proxies = []
        invalid_lines = []
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            if ':' in line:
                parts = line.split(':')
                if len(parts) >= 2:
                    ip = parts[0].strip()
                    port = parts[1].strip()
                    
                    # 简单的IP格式验证
                    ip_parts = ip.split('.')
                    if len(ip_parts) == 4 and all(part.isdigit() and 0 <= int(part) <= 255 for part in ip_parts):
                        if port.isdigit() and 1 <= int(port) <= 65535:
                            valid_proxies.append(line)
                        else:
                            invalid_lines.append(f"行{i}: 端口无效 - {line}")
                    else:
                        invalid_lines.append(f"行{i}: IP格式无效 - {line}")
                else:
                    invalid_lines.append(f"行{i}: 格式错误 - {line}")
            else:
                invalid_lines.append(f"行{i}: 缺少冒号 - {line}")
        
        print(f"📊 代理统计:")
        print(f"  总行数: {len(lines)}")
        print(f"  有效代理: {len(valid_proxies)}")
        print(f"  无效行数: {len(invalid_lines)}")
        
        if valid_proxies:
            print(f"\n✅ 找到 {len(valid_proxies)} 个有效代理")
            print("前5个代理示例:")
            for i, proxy in enumerate(valid_proxies[:5]):
                print(f"  {i+1}. {proxy}")
        else:
            print("❌ 没有找到有效代理")
            return False
        
        if invalid_lines:
            print(f"\n⚠️ 发现 {len(invalid_lines)} 个无效行:")
            for invalid in invalid_lines[:5]:  # 只显示前5个
                print(f"  {invalid}")
            if len(invalid_lines) > 5:
                print(f"  ... 还有 {len(invalid_lines) - 5} 个无效行")
        
        return len(valid_proxies) > 0
        
    except Exception as e:
        print(f"❌ 读取代理文件失败: {e}")
        return False

def check_amazon_scraper_integration():
    """检查amazon_scraper.py中的代理集成"""
    print("\n" + "=" * 60)
    print("🔧 代理集成检查")
    print("=" * 60)
    
    try:
        with open('amazon_scraper.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键类和方法是否存在
        checks = [
            ('ProxyRotator类', 'class ProxyRotator'),
            ('代理轮换方法', '_make_request_with_proxy_rotation'),
            ('代理参数', 'proxy_rotator='),
            ('代理导入', 'from collections import deque'),
            ('线程支持', 'import threading'),
            ('API路由', '@app.route(\'/api/scrape\'')
        ]
        
        all_passed = True
        
        for check_name, search_text in checks:
            if search_text in content:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name} - 未找到")
                all_passed = False
        
        if all_passed:
            print("\n✅ 代理集成检查通过")
        else:
            print("\n❌ 代理集成不完整")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查amazon_scraper.py失败: {e}")
        return False

def check_api_service():
    """检查API服务状态"""
    print("\n" + "=" * 60)
    print("🌐 API服务检查")
    print("=" * 60)
    
    api_urls = [
        "http://192.168.110.35:1122",
        "http://192.168.110.35:1122/api/scrape"
    ]
    
    for url in api_urls:
        try:
            print(f"🔗 测试: {url}")
            
            if url.endswith('/api/scrape'):
                # POST请求测试
                response = requests.post(
                    url,
                    json={"test": "connection"},
                    timeout=10
                )
            else:
                # GET请求测试
                response = requests.get(url, timeout=10)
            
            print(f"  状态码: {response.status_code}")
            
            if response.status_code in [200, 400]:  # 400也是正常的，说明服务在运行
                print(f"  ✅ 服务正常")
                
                if response.headers.get('content-type', '').startswith('application/json'):
                    try:
                        data = response.json()
                        if 'message' in data:
                            print(f"  📄 消息: {data['message']}")
                    except:
                        pass
            else:
                print(f"  ⚠️ 状态异常")
                
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 连接失败 - 服务可能未启动")
        except requests.exceptions.Timeout:
            print(f"  ❌ 连接超时")
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")

def generate_test_command():
    """生成测试命令"""
    print("\n" + "=" * 60)
    print("🧪 测试命令")
    print("=" * 60)
    
    print("环境检查完成后，可以使用以下命令进行测试:")
    print()
    print("1. 测试代理轮换功能:")
    print("   python test_api_proxy.py")
    print()
    print("2. 管理代理列表:")
    print("   python proxy_manager.py --test --save working_proxies.txt")
    print()
    print("3. 查看使用示例:")
    print("   python example_usage.py")
    print()
    print("4. 直接测试API接口:")
    print("   curl -X POST http://192.168.110.35:1122/api/scrape \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"pageContent\":\"<html>test</html>\",\"enable_variants\":true}'")

def main():
    """主检查函数"""
    print("🔍 宝塔面板Python项目环境检查")
    print(f"📁 当前目录: {os.getcwd()}")
    print("=" * 60)
    
    checks = [
        ("Python版本", check_python_version),
        ("Python模块", check_required_modules),
        ("项目文件", check_project_files),
        ("代理配置", check_proxy_file),
        ("代理集成", check_amazon_scraper_integration),
        ("API服务", check_api_service)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查异常: {e}")
            results.append((check_name, False))
    
    # 显示检查总结
    print("\n" + "=" * 60)
    print("📊 环境检查总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name}: {status}")
    
    print(f"\n📈 总体结果: {passed}/{total} 检查通过")
    
    if passed == total:
        print("🎉 环境配置完整！可以开始测试代理功能")
        generate_test_command()
    else:
        print("⚠️ 环境配置不完整，请根据上述检查结果进行修复")
        
        # 提供修复建议
        print("\n🔧 修复建议:")
        for check_name, result in results:
            if not result:
                if check_name == "Python模块":
                    print("- 在宝塔面板的Python项目管理中安装缺少的模块")
                elif check_name == "项目文件":
                    print("- 确保所有必需文件已上传到项目目录")
                elif check_name == "代理配置":
                    print("- 检查代理IP.txt文件格式，确保每行格式为 IP:PORT")
                elif check_name == "代理集成":
                    print("- 重新上传最新的amazon_scraper.py文件")
                elif check_name == "API服务":
                    print("- 在宝塔面板中启动Python项目服务")

if __name__ == "__main__":
    main()
