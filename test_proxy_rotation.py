#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理轮换机制测试脚本
用于验证代理IP轮换功能是否正常工作
"""

import sys
import os
import time
import requests

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from amazon_scraper import ProxyRotator

def test_proxy_rotator():
    """测试代理轮换器功能"""
    print("=" * 60)
    print("🧪 代理轮换器功能测试")
    print("=" * 60)
    
    # 创建代理轮换器
    try:
        rotator = ProxyRotator(
            proxy_file="代理IP.txt",
            username="xinyc1126",
            password="woaiwojia001",
            verbose=True
        )
        
        # 显示初始统计信息
        stats = rotator.get_proxy_stats()
        print(f"\n📊 初始代理统计:")
        print(f"  - 总代理数: {stats['total_proxies']}")
        print(f"  - 可用代理数: {stats['available_proxies']}")
        print(f"  - 失败代理数: {stats['failed_proxies']}")
        
        if stats['available_proxies'] == 0:
            print("❌ 没有可用代理，请检查代理IP.txt文件")
            return False
        
        # 测试代理轮换
        print(f"\n🔄 测试代理轮换 (测试5个代理):")
        test_url = "https://ipv4.webshare.io/"
        
        for i in range(min(5, stats['available_proxies'])):
            print(f"\n--- 测试 {i+1} ---")
            
            # 获取下一个代理
            proxy = rotator.get_next_proxy()
            if not proxy:
                print("❌ 无法获取代理")
                break
            
            print(f"🌐 当前代理: {proxy['http']}")
            
            # 测试代理连接
            try:
                start_time = time.time()
                response = requests.get(
                    test_url,
                    proxies=proxy,
                    timeout=10,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    print(f"✅ 代理测试成功! 响应时间: {end_time - start_time:.2f}秒")
                    print(f"📄 响应长度: {len(response.text)} 字节")
                else:
                    print(f"⚠️ 代理响应异常: 状态码 {response.status_code}")
                    rotator.mark_proxy_failed(proxy)
                    
            except Exception as e:
                print(f"❌ 代理连接失败: {str(e)}")
                rotator.mark_proxy_failed(proxy)
            
            # 显示当前统计
            current_stats = rotator.get_proxy_stats()
            print(f"📊 当前统计: 可用={current_stats['available_proxies']}, 失败={current_stats['failed_proxies']}")
            
            time.sleep(1)  # 避免请求过快
        
        # 最终统计
        final_stats = rotator.get_proxy_stats()
        print(f"\n📊 最终代理统计:")
        print(f"  - 总代理数: {final_stats['total_proxies']}")
        print(f"  - 可用代理数: {final_stats['available_proxies']}")
        print(f"  - 失败代理数: {final_stats['failed_proxies']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 代理轮换器测试失败: {e}")
        return False

def test_amazon_request_with_proxy():
    """测试使用代理访问Amazon"""
    print("\n" + "=" * 60)
    print("🛒 Amazon代理访问测试")
    print("=" * 60)
    
    try:
        rotator = ProxyRotator(
            proxy_file="代理IP.txt",
            username="xinyc1126",
            password="woaiwojia001",
            verbose=True
        )
        
        # 获取代理
        proxy = rotator.get_next_proxy()
        if not proxy:
            print("❌ 无法获取代理")
            return False
        
        print(f"🌐 使用代理: {proxy['http']}")
        
        # 测试Amazon访问
        test_urls = [
            "https://www.amazon.de",
            "https://www.amazon.com"
        ]
        
        for url in test_urls:
            print(f"\n🔗 测试访问: {url}")
            
            try:
                start_time = time.time()
                response = requests.get(
                    url,
                    proxies=proxy,
                    timeout=15,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1'
                    }
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    print(f"✅ Amazon访问成功! 响应时间: {end_time - start_time:.2f}秒")
                    print(f"📄 响应长度: {len(response.text)} 字节")
                    
                    # 检查是否被验证码拦截
                    if "captcha" in response.text.lower() or "bot check" in response.text.lower():
                        print("⚠️ 检测到验证码页面")
                    else:
                        print("✅ 正常访问Amazon页面")
                        
                elif response.status_code in [403, 429, 503]:
                    print(f"⚠️ Amazon访问被限制: 状态码 {response.status_code}")
                    rotator.mark_proxy_failed(proxy)
                else:
                    print(f"⚠️ Amazon访问异常: 状态码 {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Amazon访问失败: {str(e)}")
                rotator.mark_proxy_failed(proxy)
                
                # 尝试下一个代理
                next_proxy = rotator.get_next_proxy()
                if next_proxy:
                    proxy = next_proxy
                    print(f"🔄 切换到下一个代理: {proxy['http']}")
                else:
                    print("❌ 没有更多可用代理")
                    break
        
        return True
        
    except Exception as e:
        print(f"❌ Amazon代理访问测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始代理轮换机制测试")
    
    # 检查代理文件是否存在
    if not os.path.exists("代理IP.txt"):
        print("❌ 代理IP.txt文件不存在，请确保文件在当前目录")
        return
    
    # 测试1: 代理轮换器基本功能
    print("\n" + "🧪 测试1: 代理轮换器基本功能")
    if not test_proxy_rotator():
        print("❌ 代理轮换器基本功能测试失败")
        return
    
    # 测试2: Amazon代理访问
    print("\n" + "🧪 测试2: Amazon代理访问测试")
    if not test_amazon_request_with_proxy():
        print("❌ Amazon代理访问测试失败")
        return
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
