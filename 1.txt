================================================================================
🔍 API价格参数调试信息
================================================================================
API请求时间: 2025-08-24 09:35:24
API传入的price参数: '28.46'
price参数类型: <class 'str'>
处理后的fallback_price: '28.46'
fallback_price是否为空: False
其他相关参数:
  - enable_variants: True
  - send_to_api: True
  - verbose_debug: True

🌐 代理轮换器初始化信息
--------------------------------------------------
初始化时间: 2025-08-24 09:35:24
代理文件: 代理IP.txt
代理用户名: xinyc1126
总代理数: 250
可用代理数: 250
失败代理数: 0
当前代理: None
初始化耗时: 0.00秒


================================================================================
🔄 代理使用日志
================================================================================
日志记录时间: 2025-08-24 09:35:55
总日志条数: 22

09:35:24 - 🔍 验证代理: *************:5522
09:35:25 - ✅ 代理验证成功，切换到: *************:5522
09:35:26 - 🚀 开始批量获取变体价格: 1 个ASIN
09:35:28 - ❌ 代理被限制 (状态码: 202, 响应长度: 0 字节)
09:35:28 - ❌ 标记代理失败: *************:5522
09:35:28 - 📊 剩余可用代理: 249
09:35:28 - 🔍 验证代理: ************:6404
09:35:31 - ✅ 代理验证成功，切换到: ************:6404
09:35:31 - 🔄 重试请求 (第2次): http://xinyc1126:woaiwojia001@************:6404/
09:35:33 - ❌ 代理被限制 (状态码: 202, 响应长度: 0 字节)
09:35:33 - ❌ 标记代理失败: ************:6404
09:35:33 - 📊 剩余可用代理: 248
09:35:33 - 🔍 验证代理: 89.213.188.147:6023
09:35:36 - ❌ 代理验证失败: 89.213.188.147:6023
09:35:36 - 🔍 验证代理: 23.236.170.197:9230
09:35:37 - ✅ 代理验证成功，切换到: 23.236.170.197:9230
09:35:37 - 🔄 重试请求 (第3次): *************************************************/
09:35:49 - ❌ 标记代理失败: 23.236.170.197:9230
09:35:49 - 📊 剩余可用代理: 246
09:35:49 - 🔍 验证代理: 82.24.214.245:8044
09:35:51 - ✅ 代理验证成功，切换到: 82.24.214.245:8044
09:35:54 - ⚠️ 请求失败 (状态码: 404)

📊 最终代理统计:
总代理数: 250
可用代理数: 246
失败代理数: 4
当前代理: 82.24.214.245:8044

