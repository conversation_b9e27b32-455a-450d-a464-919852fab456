#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理轮换机制使用示例
演示如何在实际场景中使用代理轮换功能
"""

import sys
import os
import time
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from amazon_scraper import AmazonProductScraper, ProxyRotator

def example_1_basic_proxy_rotation():
    """示例1: 基本代理轮换使用"""
    print("=" * 60)
    print("📝 示例1: 基本代理轮换使用")
    print("=" * 60)
    
    # 创建代理轮换器
    proxy_rotator = ProxyRotator(
        proxy_file="代理IP.txt",
        username="xinyc1126",
        password="woaiwojia001",
        verbose=True
    )
    
    # 显示初始统计
    stats = proxy_rotator.get_proxy_stats()
    print(f"📊 代理统计: 总数={stats['total_proxies']}, 可用={stats['available_proxies']}")
    
    # 模拟多次请求，观察代理轮换
    for i in range(5):
        proxy = proxy_rotator.get_next_proxy()
        if proxy:
            print(f"🔄 请求 {i+1}: 使用代理 {proxy['http']}")
            time.sleep(1)
        else:
            print(f"❌ 请求 {i+1}: 无可用代理")
            break

def example_2_scraper_with_proxy():
    """示例2: 在爬虫中使用代理轮换"""
    print("\n" + "=" * 60)
    print("📝 示例2: 在爬虫中使用代理轮换")
    print("=" * 60)
    
    # 示例HTML内容（实际使用时替换为真实的Amazon产品页面HTML）
    sample_html = """
    <!DOCTYPE html>
    <html>
    <head><title>Amazon Product</title></head>
    <body>
        <div id="productTitle">Sample Product Title</div>
        <div class="a-price">
            <span class="a-offscreen">€29.99</span>
        </div>
        <div id="twister">
            <!-- 变体数据会在这里 -->
        </div>
    </body>
    </html>
    """
    
    try:
        # 创建代理轮换器
        proxy_rotator = ProxyRotator(
            proxy_file="代理IP.txt",
            username="xinyc1126",
            password="woaiwojia001",
            verbose=True
        )
        
        # 创建爬虫实例，集成代理轮换
        scraper = AmazonProductScraper(
            html_content=sample_html,
            verbose=True,
            enable_variants=True,
            proxy_rotator=proxy_rotator  # 传入代理轮换器
        )
        
        # 加载HTML
        if scraper.load_html():
            print("✅ HTML加载成功")
            
            # 提取产品信息
            if scraper.extract_product_info():
                print("✅ 产品信息提取成功")
                
                # 显示提取的信息
                product_data = scraper.product_data
                print(f"📦 产品标题: {product_data.get('产品标题', 'N/A')}")
                print(f"💰 产品价格: {product_data.get('价格', 'N/A')}")
                print(f"🔢 变体数量: {len(product_data.get('产品变体', []))}")
                
            else:
                print("❌ 产品信息提取失败")
        else:
            print("❌ HTML加载失败")
            
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")

def example_3_api_simulation():
    """示例3: 模拟API调用场景"""
    print("\n" + "=" * 60)
    print("📝 示例3: 模拟API调用场景")
    print("=" * 60)
    
    # 模拟API请求数据
    api_request_data = {
        "html_content": """
        <!DOCTYPE html>
        <html>
        <head><title>Multi-Variant Amazon Product</title></head>
        <body>
            <div id="productTitle">Multi-Variant Product</div>
            <div class="a-price">
                <span class="a-offscreen">€39.99</span>
            </div>
            <script type="text/javascript">
                var twisterData = {
                    "dimensionValuesData": {
                        "B001": {"price": "39.99"},
                        "B002": {"price": "42.99"},
                        "B003": {"price": "45.99"}
                    }
                };
            </script>
        </body>
        </html>
        """,
        "enable_variants": True,
        "warehouse_id": "3079",
        "send_to_api": False,  # 不实际发送到API
        "verbose_debug": True
    }
    
    print("📨 模拟API请求数据:")
    print(f"  - enable_variants: {api_request_data['enable_variants']}")
    print(f"  - warehouse_id: {api_request_data['warehouse_id']}")
    print(f"  - HTML长度: {len(api_request_data['html_content'])} 字符")
    
    try:
        # 创建代理轮换器（在实际API中会自动创建）
        proxy_rotator = ProxyRotator(
            proxy_file="代理IP.txt",
            username="xinyc1126",
            password="woaiwojia001",
            verbose=True
        )
        
        # 创建爬虫实例
        scraper = AmazonProductScraper(
            html_content=api_request_data["html_content"],
            verbose=api_request_data["verbose_debug"],
            enable_variants=api_request_data["enable_variants"],
            proxy_rotator=proxy_rotator
        )
        
        # 处理请求
        if scraper.load_html() and scraper.extract_product_info():
            print("✅ API请求处理成功")
            
            # 构建响应数据
            response_data = {
                "success": True,
                "data": {
                    "title": scraper.product_data.get('产品标题', ''),
                    "price": scraper.product_data.get('价格', ''),
                    "variants": scraper.product_data.get('产品变体', []),
                    "images": scraper.product_data.get('产品图片', [])
                },
                "proxy_stats": proxy_rotator.get_proxy_stats() if proxy_rotator else None
            }
            
            print("📤 API响应数据:")
            print(f"  - 成功状态: {response_data['success']}")
            print(f"  - 产品标题: {response_data['data']['title']}")
            print(f"  - 产品价格: {response_data['data']['price']}")
            print(f"  - 变体数量: {len(response_data['data']['variants'])}")
            
            if response_data['proxy_stats']:
                stats = response_data['proxy_stats']
                print(f"  - 代理统计: 可用={stats['available_proxies']}, 失败={stats['failed_proxies']}")
            
        else:
            print("❌ API请求处理失败")
            
    except Exception as e:
        print(f"❌ API模拟执行失败: {e}")

def example_4_error_handling():
    """示例4: 错误处理和代理切换"""
    print("\n" + "=" * 60)
    print("📝 示例4: 错误处理和代理切换演示")
    print("=" * 60)
    
    try:
        # 创建代理轮换器
        proxy_rotator = ProxyRotator(
            proxy_file="代理IP.txt",
            username="xinyc1126",
            password="woaiwojia001",
            verbose=True
        )
        
        # 模拟代理失败场景
        print("🧪 模拟代理失败场景:")
        
        # 获取几个代理
        proxies_to_test = []
        for i in range(min(3, proxy_rotator.get_proxy_stats()['available_proxies'])):
            proxy = proxy_rotator.get_next_proxy()
            if proxy:
                proxies_to_test.append(proxy)
        
        # 模拟标记代理失败
        for i, proxy in enumerate(proxies_to_test):
            print(f"\n🔄 测试代理 {i+1}: {proxy['http']}")
            
            # 模拟请求失败（实际场景中这里会是真实的HTTP请求）
            print("❌ 模拟请求失败 (403 Forbidden)")
            proxy_rotator.mark_proxy_failed(proxy)
            
            # 显示当前统计
            stats = proxy_rotator.get_proxy_stats()
            print(f"📊 当前统计: 可用={stats['available_proxies']}, 失败={stats['failed_proxies']}")
            
            # 获取下一个代理
            next_proxy = proxy_rotator.get_next_proxy()
            if next_proxy:
                print(f"🔄 切换到下一个代理: {next_proxy['http']}")
            else:
                print("❌ 没有更多可用代理")
                break
        
        # 最终统计
        final_stats = proxy_rotator.get_proxy_stats()
        print(f"\n📊 最终统计:")
        print(f"  - 总代理数: {final_stats['total_proxies']}")
        print(f"  - 可用代理数: {final_stats['available_proxies']}")
        print(f"  - 失败代理数: {final_stats['failed_proxies']}")
        
    except Exception as e:
        print(f"❌ 错误处理示例执行失败: {e}")

def main():
    """主函数 - 运行所有示例"""
    print("🚀 代理轮换机制使用示例")
    print("=" * 60)
    
    # 检查代理文件
    if not os.path.exists("代理IP.txt"):
        print("❌ 代理IP.txt文件不存在")
        print("请确保代理文件存在于当前目录")
        return
    
    try:
        # 运行示例
        example_1_basic_proxy_rotation()
        example_2_scraper_with_proxy()
        example_3_api_simulation()
        example_4_error_handling()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例执行完成!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {e}")

if __name__ == "__main__":
    main()
