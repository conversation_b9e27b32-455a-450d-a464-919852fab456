#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API代理功能测试脚本
测试宝塔面板中的Amazon爬虫API接口的代理轮换功能
"""

import requests
import json
import time
import base64
import os

# API配置
API_BASE_URL = "http://192.168.110.35:1122"
API_ENDPOINT = "/api/scrape"
API_URL = f"{API_BASE_URL}{API_ENDPOINT}"

def load_sample_html():
    """加载示例HTML内容"""
    # 多变体产品的示例HTML
    sample_html = """
    <!DOCTYPE html>
    <html lang="de">
    <head>
        <title>Amazon.de: Multi-Variant Product</title>
        <meta charset="utf-8">
    </head>
    <body>
        <div id="dp-container">
            <div id="centerCol">
                <div id="productTitle" class="a-size-large product-title-word-break">
                    <span>Test Multi-Variant Product with Proxy Rotation</span>
                </div>
                
                <div class="a-section a-spacing-none aok-align-center">
                    <div class="a-price a-text-price a-size-medium apexPriceToPay">
                        <span class="a-offscreen">€45.99</span>
                        <span class="a-price-symbol">€</span>
                        <span class="a-price-whole">45</span>
                        <span class="a-price-fraction">99</span>
                    </div>
                </div>
                
                <div id="twister" class="a-section a-spacing-base">
                    <div class="a-row">
                        <div class="a-column a-span12">
                            <div id="variation_color_name" class="a-section">
                                <div class="a-row">
                                    <span class="selection">Farbe:</span>
                                    <span class="a-dropdown-container">
                                        <select name="dropdown_selected_color_name">
                                            <option value="0">Wählen Sie</option>
                                            <option value="1" data-asin="B001TEST01">Rot</option>
                                            <option value="2" data-asin="B001TEST02">Blau</option>
                                            <option value="3" data-asin="B001TEST03">Grün</option>
                                        </select>
                                    </span>
                                </div>
                            </div>
                            
                            <div id="variation_size_name" class="a-section">
                                <div class="a-row">
                                    <span class="selection">Größe:</span>
                                    <span class="a-dropdown-container">
                                        <select name="dropdown_selected_size_name">
                                            <option value="0">Wählen Sie</option>
                                            <option value="1" data-asin="B001TEST01">S</option>
                                            <option value="2" data-asin="B001TEST02">M</option>
                                            <option value="3" data-asin="B001TEST03">L</option>
                                        </select>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <script type="text/javascript">
                    var twisterData = {
                        "dimensionValuesData": {
                            "B001TEST01": {
                                "price": "42.99",
                                "priceFormatted": "€42,99",
                                "availability": "InStock"
                            },
                            "B001TEST02": {
                                "price": "45.99", 
                                "priceFormatted": "€45,99",
                                "availability": "InStock"
                            },
                            "B001TEST03": {
                                "price": "48.99",
                                "priceFormatted": "€48,99", 
                                "availability": "InStock"
                            }
                        },
                        "dimensionDisplayData": {
                            "color_name": {
                                "1": {"displayValue": "Rot"},
                                "2": {"displayValue": "Blau"},
                                "3": {"displayValue": "Grün"}
                            },
                            "size_name": {
                                "1": {"displayValue": "S"},
                                "2": {"displayValue": "M"}, 
                                "3": {"displayValue": "L"}
                            }
                        }
                    };
                </script>
            </div>
        </div>
    </body>
    </html>
    """
    return sample_html

def test_api_basic():
    """测试API基本功能"""
    print("=" * 60)
    print("🧪 测试1: API基本功能测试")
    print("=" * 60)
    
    try:
        # 测试API健康检查
        health_response = requests.get(API_BASE_URL, timeout=10)
        if health_response.status_code == 200:
            print("✅ API服务正常运行")
            print(f"📄 API信息: {health_response.json().get('message', 'N/A')}")
        else:
            print(f"⚠️ API健康检查异常: {health_response.status_code}")
            
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False
    
    return True

def test_api_without_variants():
    """测试API不启用变体功能"""
    print("\n" + "=" * 60)
    print("🧪 测试2: API不启用变体功能（不使用代理）")
    print("=" * 60)
    
    html_content = load_sample_html()
    
    payload = {
        "pageContent": html_content,
        "warehouse_id": "3079",
        "send_to_api": False,  # 不发送到WMS API
        "enable_variants": False,  # 不启用变体（不使用代理）
        "verbose_debug": True
    }
    
    try:
        print(f"📤 发送请求到: {API_URL}")
        print(f"📊 请求参数: enable_variants={payload['enable_variants']}")
        
        start_time = time.time()
        response = requests.post(
            API_URL,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        end_time = time.time()
        
        print(f"⏱️ 请求耗时: {end_time - start_time:.2f}秒")
        print(f"📈 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API请求成功")
            print(f"📦 提取成功: {result.get('success', False)}")
            
            if result.get('success'):
                data = result.get('data', {})
                print(f"📝 产品标题: {data.get('title', 'N/A')}")
                print(f"💰 产品价格: {data.get('price', 'N/A')}")
                print(f"🔢 变体数量: {len(data.get('variants', []))}")
                print("ℹ️ 注意: 未启用变体，不会使用代理轮换")
            else:
                print(f"❌ 提取失败: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"📄 响应内容: {response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False
    
    return True

def test_api_with_variants():
    """测试API启用变体功能（使用代理轮换）"""
    print("\n" + "=" * 60)
    print("🧪 测试3: API启用变体功能（使用代理轮换）")
    print("=" * 60)
    
    html_content = load_sample_html()
    
    payload = {
        "pageContent": html_content,
        "warehouse_id": "3079", 
        "send_to_api": False,  # 不发送到WMS API
        "enable_variants": True,  # 启用变体（使用代理）
        "verbose_debug": True
    }
    
    try:
        print(f"📤 发送请求到: {API_URL}")
        print(f"📊 请求参数: enable_variants={payload['enable_variants']}")
        print("🔄 注意: 启用变体功能，将自动使用代理轮换机制")
        
        start_time = time.time()
        response = requests.post(
            API_URL,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=60  # 增加超时时间，因为代理请求可能较慢
        )
        end_time = time.time()
        
        print(f"⏱️ 请求耗时: {end_time - start_time:.2f}秒")
        print(f"📈 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API请求成功")
            print(f"📦 提取成功: {result.get('success', False)}")
            
            if result.get('success'):
                data = result.get('data', {})
                print(f"📝 产品标题: {data.get('title', 'N/A')}")
                print(f"💰 产品价格: {data.get('price', 'N/A')}")
                print(f"🔢 变体数量: {len(data.get('variants', []))}")
                
                # 显示变体信息
                variants = data.get('variants', [])
                if variants:
                    print("🎯 变体详情:")
                    for i, variant in enumerate(variants[:3]):  # 只显示前3个
                        print(f"  变体 {i+1}:")
                        print(f"    ASIN: {variant.get('asin', 'N/A')}")
                        print(f"    价格: {variant.get('sale_price', 'N/A')}")
                        print(f"    库存: {variant.get('stock_quantity', 'N/A')}")
                
                # 检查调试信息中的代理使用情况
                debug_info = result.get('debug_info', '')
                if debug_info:
                    if '代理轮换器初始化完成' in debug_info:
                        print("✅ 代理轮换器已成功初始化")
                    if '切换到代理' in debug_info:
                        print("✅ 检测到代理切换活动")
                    if '代理验证' in debug_info:
                        print("✅ 检测到代理验证活动")
                        
            else:
                print(f"❌ 提取失败: {result.get('error', 'Unknown error')}")
                
                # 检查是否是代理相关错误
                error_msg = result.get('error', '')
                if '代理' in error_msg or 'proxy' in error_msg.lower():
                    print("⚠️ 可能是代理相关问题，请检查代理IP.txt文件")
                    
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"📄 响应内容: {response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False
    
    return True

def test_api_multiple_requests():
    """测试多次API请求观察代理轮换"""
    print("\n" + "=" * 60)
    print("🧪 测试4: 多次API请求观察代理轮换")
    print("=" * 60)
    
    html_content = load_sample_html()
    
    payload = {
        "pageContent": html_content,
        "warehouse_id": "3079",
        "send_to_api": False,
        "enable_variants": True,
        "verbose_debug": True
    }
    
    success_count = 0
    total_requests = 3
    
    for i in range(total_requests):
        print(f"\n--- 请求 {i+1}/{total_requests} ---")
        
        try:
            start_time = time.time()
            response = requests.post(
                API_URL,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=60
            )
            end_time = time.time()
            
            print(f"⏱️ 请求耗时: {end_time - start_time:.2f}秒")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    success_count += 1
                    print(f"✅ 请求 {i+1} 成功")
                    
                    # 检查变体数量
                    variants = result.get('data', {}).get('variants', [])
                    print(f"🔢 获取到 {len(variants)} 个变体")
                else:
                    print(f"❌ 请求 {i+1} 失败: {result.get('error', 'Unknown')}")
            else:
                print(f"❌ 请求 {i+1} HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求 {i+1} 异常: {e}")
        
        # 请求间隔
        if i < total_requests - 1:
            time.sleep(2)
    
    print(f"\n📊 测试结果: {success_count}/{total_requests} 请求成功")
    success_rate = (success_count / total_requests) * 100
    print(f"📈 成功率: {success_rate:.1f}%")
    
    return success_count > 0

def check_proxy_file():
    """检查代理文件是否存在"""
    print("=" * 60)
    print("🔍 检查代理配置")
    print("=" * 60)
    
    proxy_file = "代理IP.txt"
    
    if os.path.exists(proxy_file):
        try:
            with open(proxy_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            proxy_count = len([line for line in lines if line.strip() and ':' in line])
            print(f"✅ 代理文件存在: {proxy_file}")
            print(f"📊 代理数量: {proxy_count}")
            
            if proxy_count > 0:
                print("📝 前5个代理示例:")
                count = 0
                for line in lines:
                    line = line.strip()
                    if line and ':' in line:
                        print(f"  {count+1}. {line}")
                        count += 1
                        if count >= 5:
                            break
            else:
                print("⚠️ 代理文件为空或格式不正确")
                return False
                
        except Exception as e:
            print(f"❌ 读取代理文件失败: {e}")
            return False
    else:
        print(f"❌ 代理文件不存在: {proxy_file}")
        print("请确保代理文件存在于项目根目录")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 Amazon爬虫API代理功能测试")
    print(f"🌐 API地址: {API_URL}")
    print("=" * 60)
    
    # 检查代理文件
    if not check_proxy_file():
        print("\n❌ 代理文件检查失败，无法继续测试")
        return
    
    # 执行测试
    tests = [
        ("API基本功能", test_api_basic),
        ("不启用变体", test_api_without_variants), 
        ("启用变体+代理", test_api_with_variants),
        ("多次请求测试", test_api_multiple_requests)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 开始测试: {test_name}")
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
                
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！代理轮换功能正常工作")
    else:
        print("⚠️ 部分测试失败，请检查配置和日志")

if __name__ == "__main__":
    main()
