#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单API测试脚本
快速测试宝塔面板中的Amazon爬虫API代理功能
"""

import requests
import json
import time

# API配置
API_URL = "http://**************:1122/api/scrape"

def create_test_html():
    """创建测试用的HTML内容"""
    return """
<!DOCTYPE html>
<html lang="de">
<head>
    <title>Amazon.de Test Product</title>
</head>
<body>
    <div id="dp-container">
        <div id="centerCol">
            <div id="productTitle">
                <span>Test Product with Multiple Variants</span>
            </div>
            
            <div class="a-price">
                <span class="a-offscreen">€29.99</span>
            </div>
            
            <div id="twister">
                <select name="dropdown_selected_color_name">
                    <option value="1" data-asin="B001">Red</option>
                    <option value="2" data-asin="B002">Blue</option>
                    <option value="3" data-asin="B003">Green</option>
                </select>
            </div>
            
            <script type="text/javascript">
                var twisterData = {
                    "dimensionValuesData": {
                        "B001": {"price": "29.99"},
                        "B002": {"price": "32.99"},
                        "B003": {"price": "35.99"}
                    }
                };
            </script>
        </div>
    </div>
</body>
</html>
"""

def test_without_proxy():
    """测试不使用代理（不启用变体）"""
    print("🧪 测试1: 不使用代理（enable_variants=false）")
    print("-" * 50)
    
    payload = {
        "pageContent": create_test_html(),
        "warehouse_id": "3079",
        "send_to_api": False,
        "enable_variants": False,  # 不启用变体，不使用代理
        "verbose_debug": True
    }
    
    try:
        start_time = time.time()
        response = requests.post(API_URL, json=payload, timeout=30)
        end_time = time.time()
        
        print(f"⏱️ 请求耗时: {end_time - start_time:.2f}秒")
        print(f"📈 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功: {result.get('success', False)}")
            
            if result.get('success'):
                data = result.get('data', {})
                print(f"📦 标题: {data.get('title', 'N/A')}")
                print(f"💰 价格: {data.get('price', 'N/A')}")
                print(f"🔢 变体数: {len(data.get('variants', []))}")
            else:
                print(f"❌ 错误: {result.get('error', 'Unknown')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def test_with_proxy():
    """测试使用代理（启用变体）"""
    print("\n🧪 测试2: 使用代理（enable_variants=true）")
    print("-" * 50)
    
    payload = {
        "pageContent": create_test_html(),
        "warehouse_id": "3079",
        "send_to_api": False,
        "enable_variants": True,  # 启用变体，使用代理
        "verbose_debug": True
    }
    
    try:
        start_time = time.time()
        response = requests.post(API_URL, json=payload, timeout=60)
        end_time = time.time()
        
        print(f"⏱️ 请求耗时: {end_time - start_time:.2f}秒")
        print(f"📈 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功: {result.get('success', False)}")
            
            if result.get('success'):
                data = result.get('data', {})
                print(f"📦 标题: {data.get('title', 'N/A')}")
                print(f"💰 价格: {data.get('price', 'N/A')}")
                print(f"🔢 变体数: {len(data.get('variants', []))}")
                
                # 检查调试信息中的代理相关内容
                debug_info = result.get('debug_info', '')
                if debug_info:
                    proxy_keywords = ['代理轮换器', '切换到代理', '代理验证', 'proxy']
                    found_proxy_activity = any(keyword in debug_info for keyword in proxy_keywords)
                    
                    if found_proxy_activity:
                        print("🔄 ✅ 检测到代理活动")
                    else:
                        print("🔄 ⚠️ 未检测到代理活动")
                        
                    # 显示部分调试信息
                    debug_lines = debug_info.split('\n')
                    proxy_lines = [line for line in debug_lines if any(keyword in line for keyword in proxy_keywords)]
                    if proxy_lines:
                        print("📝 代理相关日志:")
                        for line in proxy_lines[:3]:  # 只显示前3行
                            print(f"   {line.strip()}")
            else:
                print(f"❌ 错误: {result.get('error', 'Unknown')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def test_api_health():
    """测试API健康状态"""
    print("🧪 测试0: API健康检查")
    print("-" * 50)
    
    try:
        base_url = "http://**************:1122"
        response = requests.get(base_url, timeout=10)
        
        print(f"📈 状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ API服务正常")
                print(f"📄 消息: {data.get('message', 'N/A')}")
                return True
            except:
                print("✅ API服务响应，但非JSON格式")
                return True
        else:
            print(f"⚠️ API响应异常")
            return False
            
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Amazon爬虫API代理功能快速测试")
    print(f"🌐 API地址: {API_URL}")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("API健康检查", test_api_health),
        ("不使用代理", test_without_proxy),
        ("使用代理轮换", test_with_proxy)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} - 通过\n")
            else:
                print(f"❌ {test_name} - 失败\n")
                
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}\n")
            results.append((test_name, False))
    
    # 显示结果总结
    print("=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    # 提供建议
    if passed == total:
        print("🎉 所有测试通过！代理轮换功能正常工作")
    else:
        print("\n🔧 故障排除建议:")
        
        for test_name, result in results:
            if not result:
                if "API健康检查" in test_name:
                    print("- 检查宝塔面板中Python项目是否正常启动")
                    print("- 确认端口1122是否正确开放")
                elif "不使用代理" in test_name:
                    print("- 检查amazon_scraper.py基本功能是否正常")
                elif "使用代理" in test_name:
                    print("- 检查代理IP.txt文件是否存在且格式正确")
                    print("- 确认代理轮换器是否正确集成")
                    print("- 检查代理IP是否可用")

if __name__ == "__main__":
    main()
