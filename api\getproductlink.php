<?php
/**
 * 接口: GET /crawl/getNextProduct.php
 * 功能: 领取一条待处理的产品数据 (status=0)，并将 status 更新为 1
 * 参数(可选):
 *   warehouse_id      指定仓库，仅返回对应仓库数据
 *   min_sales         仅领取销售量大于该值的产品（可选，整数）；兼容别名 sales
 *   delete_id         根据ID删除该产品（可选，整数；仅当提供时执行删除并返回结果）
 *   delete_ids        批量删除多个ID（可选，逗号分隔的ID列表，如：27,28,29）
 *   update_status_id  删除指定ID的数据（可选，整数或逗号分隔的ID列表）
 *   update_status3_id 将指定ID的状态更新为3（可选，整数或逗号分隔的ID列表）
 *   readonly          只读模式（可选，1表示只读取不修改状态，0或不传表示正常模式会修改状态为1）
 * 返回:
 *   code 0   有数据
 *   code 1   暂无待处理数据
 *   其他      错误
 */

declare(strict_types=1);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['code' => -1, 'msg' => 'Method Not Allowed'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

require_once __DIR__ . '/db.php';
$pdo = get_pdo();

$warehouseId = isset($_GET['warehouse_id']) ? (int)$_GET['warehouse_id'] : 0;
$minSales   = isset($_GET['min_sales']) ? (int)$_GET['min_sales'] : (isset($_GET['sales']) ? (int)$_GET['sales'] : null);
$deleteId   = isset($_GET['delete_id']) ? (int)$_GET['delete_id'] : 0;
$deleteIds  = isset($_GET['delete_ids']) ? $_GET['delete_ids'] : ''; // 批量删除参数
$updateStatusId = isset($_GET['update_status_id']) ? $_GET['update_status_id'] : ''; // 删除数据的参数
$updateStatus3Id = isset($_GET['update_status3_id']) ? $_GET['update_status3_id'] : ''; // 更新状态为3的参数
$readonly   = isset($_GET['readonly']) ? (int)$_GET['readonly'] : 0; // 新增只读参数

// 删除数据分支：当传入 update_status_id 时，删除指定ID的数据
if (!empty($updateStatusId)) {
    try {
        $ids = array_filter(array_map('intval', explode(',', $updateStatusId)));
        if (empty($ids)) {
            echo json_encode([
                'code' => -1,
                'msg' => 'invalid update_status_id parameter'
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            exit;
        }

        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $stmt = $pdo->prepare("DELETE FROM amazon_products WHERE id IN ($placeholders)");
        $stmt->execute($ids);
        $affected = (int)$stmt->rowCount();

        echo json_encode([
            'code' => 0,
            'msg' => 'deleted',
            'data' => [
                'ids' => $ids,
                'affected_rows' => $affected
            ]
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    } catch (Throwable $e) {
        http_response_code(500);
        echo json_encode([
            'code' => -1,
            'msg' => 'server error',
            'error' => $e->getMessage()
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
    exit;
}

// 更新状态分支：当传入 update_status3_id 时，将指定ID状态更新为3
if (!empty($updateStatus3Id)) {
    try {
        $ids = array_filter(array_map('intval', explode(',', $updateStatus3Id)));
        if (empty($ids)) {
            echo json_encode([
                'code' => -1,
                'msg' => 'invalid update_status3_id parameter'
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            exit;
        }
        
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $stmt = $pdo->prepare("UPDATE amazon_products SET status = 3 WHERE id IN ($placeholders)");
        $stmt->execute($ids);
        $affected = (int)$stmt->rowCount();
        
        echo json_encode([
            'code' => 0,
            'msg' => 'status updated',
            'data' => [
                'ids' => $ids,
                'new_status' => 3,
                'affected_rows' => $affected
            ]
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    } catch (Throwable $e) {
        http_response_code(500);
        echo json_encode([
            'code' => -1,
            'msg' => 'server error',
            'error' => $e->getMessage()
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
    exit;
}

// 删除分支：当传入 delete_id 或 delete_ids 时，按ID删除并返回
if ($deleteId > 0 || !empty($deleteIds)) {
    try {
        if ($deleteId > 0) {
            // 单个删除
            $stmt = $pdo->prepare('DELETE FROM amazon_products WHERE id = :id');
            $stmt->execute([':id' => $deleteId]);
            $affected = (int)$stmt->rowCount();
            echo json_encode([
                'code' => 0,
                'msg' => 'deleted',
                'data' => [
                    'id' => $deleteId,
                    'affected_rows' => $affected
                ]
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        } else {
            // 批量删除
            $ids = array_filter(array_map('intval', explode(',', $deleteIds)));
            if (empty($ids)) {
                echo json_encode([
                    'code' => -1,
                    'msg' => 'invalid delete_ids parameter'
                ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                exit;
            }
            
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            $stmt = $pdo->prepare("DELETE FROM amazon_products WHERE id IN ($placeholders)");
            $stmt->execute($ids);
            $affected = (int)$stmt->rowCount();
            
            echo json_encode([
                'code' => 0,
                'msg' => 'deleted',
                'data' => [
                    'ids' => $ids,
                    'affected_rows' => $affected
                ]
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }
    } catch (Throwable $e) {
        http_response_code(500);
        echo json_encode([
            'code' => -1,
            'msg' => 'server error',
            'error' => $e->getMessage()
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
    exit;
}

try {
    // 只读模式不需要事务和行锁，正常模式需要
    if ($readonly !== 1) {
        $pdo->beginTransaction();
    }

    $conditions = ['status = 0'];
    $params     = [];
    if ($warehouseId > 0) {
        $conditions[] = 'warehouse_id = :wid';
        $params[':wid'] = $warehouseId;
    }
    if ($minSales !== null) {
        $conditions[] = 'sales > :min_sales';
        $params[':min_sales'] = $minSales;
    }

    // 只读模式不加 FOR UPDATE 锁，正常模式加锁
    $forUpdate = ($readonly === 1) ? '' : 'FOR UPDATE';
    $sql = sprintf(
        'SELECT id, product_url, price, sales, warehouse_id 
         FROM amazon_products 
         WHERE %s 
         ORDER BY id ASC 
         LIMIT 1 %s',
        implode(' AND ', $conditions),
        $forUpdate
    );
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $row = $stmt->fetch();

    if (!$row) {
        if ($readonly !== 1) {
            $pdo->commit();
        }
        echo json_encode(['code' => 1, 'msg' => 'no data'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }

    // 只有非只读模式才更新状态
    if ($readonly !== 1) {
        $update = $pdo->prepare('UPDATE amazon_products SET status = 1 WHERE id = :id');
        $update->execute([':id' => $row['id']]);
        $pdo->commit();
    }

    // 补全 https://www. 前缀
    $productUrl = $row['product_url'];
    if (stripos($productUrl, 'http') !== 0) {
        $productUrl = 'https://www.' . ltrim($productUrl, '/');
    } else {
        $parsed = parse_url($productUrl);
        if (!empty($parsed['host']) && stripos($parsed['host'], 'www.') !== 0) {
            $productUrl = $parsed['scheme'] . '://www.' . $parsed['host'] . (isset($parsed['path']) ? $parsed['path'] : '');
        }
    }

    $result = [
        'id'          => (int)$row['id'],
        'product_url' => $productUrl,
        'price'       => $row['price'] !== null ? (float)$row['price'] : null,
        'sales'       => (int)$row['sales'],
        'warehouse_id'=> isset($row['warehouse_id']) ? (int)$row['warehouse_id'] : null,
    ];

    echo json_encode(
        ['code' => 0, 'msg' => 'success', 'data' => $result],
        JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
    );
} catch (Throwable $e) {
    if ($readonly !== 1 && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500);
    echo json_encode(
        ['code' => -1, 'msg' => 'server error', 'error' => $e->getMessage()],
        JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
    );
}