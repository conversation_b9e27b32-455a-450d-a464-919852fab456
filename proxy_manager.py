#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理管理工具
用于管理和测试代理IP列表
"""

import os
import sys
import time
import requests
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from amazon_scraper import ProxyRotator

class ProxyManager:
    """代理管理器"""
    
    def __init__(self, proxy_file="代理IP.txt", username="xinyc1126", password="woaiwojia001"):
        self.proxy_file = proxy_file
        self.username = username
        self.password = password
        self.working_proxies = []
        self.failed_proxies = []
    
    def load_proxies_from_file(self):
        """从文件加载代理列表"""
        proxies = []
        
        if not os.path.exists(self.proxy_file):
            print(f"❌ 代理文件不存在: {self.proxy_file}")
            return proxies
        
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line and ':' in line:
                    parts = line.split(':')
                    if len(parts) >= 2:
                        ip = parts[0].strip()
                        port = parts[1].strip()
                        if ip and port:
                            proxy_dict = {
                                "http": f"http://{self.username}:{self.password}@{ip}:{port}/",
                                "https": f"http://{self.username}:{self.password}@{ip}:{port}/"
                            }
                            proxies.append({
                                'key': f"{ip}:{port}",
                                'proxy': proxy_dict,
                                'ip': ip,
                                'port': port
                            })
            
            print(f"✅ 从文件加载了 {len(proxies)} 个代理")
            return proxies
            
        except Exception as e:
            print(f"❌ 加载代理文件时出错: {e}")
            return proxies
    
    def test_single_proxy(self, proxy_info, test_url="https://ipv4.webshare.io/", timeout=10):
        """测试单个代理"""
        proxy_key = proxy_info['key']
        proxy_dict = proxy_info['proxy']
        
        try:
            start_time = time.time()
            response = requests.get(
                test_url,
                proxies=proxy_dict,
                timeout=timeout,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            end_time = time.time()
            
            if response.status_code == 200:
                response_time = end_time - start_time
                return {
                    'key': proxy_key,
                    'status': 'success',
                    'response_time': response_time,
                    'proxy_info': proxy_info
                }
            else:
                return {
                    'key': proxy_key,
                    'status': 'failed',
                    'error': f"HTTP {response.status_code}",
                    'proxy_info': proxy_info
                }
                
        except Exception as e:
            return {
                'key': proxy_key,
                'status': 'failed',
                'error': str(e),
                'proxy_info': proxy_info
            }
    
    def test_all_proxies(self, max_workers=10, timeout=10):
        """并发测试所有代理"""
        proxies = self.load_proxies_from_file()
        
        if not proxies:
            print("❌ 没有可测试的代理")
            return
        
        print(f"🧪 开始测试 {len(proxies)} 个代理 (并发数: {max_workers})")
        print("-" * 60)
        
        self.working_proxies = []
        self.failed_proxies = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有测试任务
            future_to_proxy = {
                executor.submit(self.test_single_proxy, proxy, timeout=timeout): proxy 
                for proxy in proxies
            }
            
            # 处理完成的任务
            completed = 0
            for future in as_completed(future_to_proxy):
                completed += 1
                result = future.result()
                
                if result['status'] == 'success':
                    self.working_proxies.append(result['proxy_info'])
                    print(f"✅ [{completed:3d}/{len(proxies)}] {result['key']} - 响应时间: {result['response_time']:.2f}s")
                else:
                    self.failed_proxies.append(result['proxy_info'])
                    print(f"❌ [{completed:3d}/{len(proxies)}] {result['key']} - 错误: {result['error']}")
        
        # 显示测试结果
        print("-" * 60)
        print(f"📊 测试完成:")
        print(f"  ✅ 可用代理: {len(self.working_proxies)}")
        print(f"  ❌ 失败代理: {len(self.failed_proxies)}")
        print(f"  📈 成功率: {len(self.working_proxies)/len(proxies)*100:.1f}%")
    
    def save_working_proxies(self, output_file="working_proxies.txt"):
        """保存可用代理到文件"""
        if not self.working_proxies:
            print("❌ 没有可用代理可保存")
            return
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                for proxy_info in self.working_proxies:
                    f.write(f"{proxy_info['key']}\n")
            
            print(f"✅ 已保存 {len(self.working_proxies)} 个可用代理到 {output_file}")
            
        except Exception as e:
            print(f"❌ 保存代理文件时出错: {e}")
    
    def show_proxy_stats(self):
        """显示代理统计信息"""
        proxies = self.load_proxies_from_file()
        
        print("📊 代理统计信息:")
        print(f"  📁 代理文件: {self.proxy_file}")
        print(f"  📝 总代理数: {len(proxies)}")
        print(f"  👤 用户名: {self.username}")
        print(f"  🔑 密码: {'*' * len(self.password)}")
        
        if self.working_proxies or self.failed_proxies:
            print(f"  ✅ 可用代理: {len(self.working_proxies)}")
            print(f"  ❌ 失败代理: {len(self.failed_proxies)}")
            if len(proxies) > 0:
                print(f"  📈 成功率: {len(self.working_proxies)/len(proxies)*100:.1f}%")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='代理管理工具')
    parser.add_argument('--file', '-f', default='代理IP.txt', help='代理文件路径')
    parser.add_argument('--username', '-u', default='xinyc1126', help='代理用户名')
    parser.add_argument('--password', '-p', default='woaiwojia001', help='代理密码')
    parser.add_argument('--test', '-t', action='store_true', help='测试所有代理')
    parser.add_argument('--workers', '-w', type=int, default=10, help='并发测试数量')
    parser.add_argument('--timeout', type=int, default=10, help='测试超时时间(秒)')
    parser.add_argument('--save', '-s', help='保存可用代理到指定文件')
    parser.add_argument('--stats', action='store_true', help='显示代理统计信息')
    
    args = parser.parse_args()
    
    # 创建代理管理器
    manager = ProxyManager(
        proxy_file=args.file,
        username=args.username,
        password=args.password
    )
    
    # 显示统计信息
    if args.stats or not any([args.test, args.save]):
        manager.show_proxy_stats()
    
    # 测试代理
    if args.test:
        manager.test_all_proxies(
            max_workers=args.workers,
            timeout=args.timeout
        )
        
        # 如果指定了保存文件，自动保存可用代理
        if args.save:
            manager.save_working_proxies(args.save)
    
    # 仅保存可用代理（需要先测试）
    elif args.save and not args.test:
        print("❌ 请先使用 --test 参数测试代理，然后再保存")

if __name__ == "__main__":
    main()
