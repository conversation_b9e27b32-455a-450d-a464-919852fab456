# 宝塔面板代理轮换功能部署指南

## 📋 概述

本指南详细说明如何在宝塔面板中部署和配置Amazon爬虫API的代理轮换功能。

## 🎯 部署环境

- **服务器**: **************
- **端口**: 1122
- **API接口**: http://**************:1122/api/scrape
- **项目路径**: Y:\**************\amaon\ifyapi\

## 📁 文件清单

### 必需文件
- ✅ `amazon_scraper.py` - 主爬虫脚本（已集成代理轮换）
- ✅ `代理IP.txt` - 代理IP列表文件

### 测试文件
- 🧪 `check_environment.py` - 环境配置检查
- 🧪 `simple_api_test.py` - 简单API测试
- 🧪 `test_api_proxy.py` - 完整代理功能测试

### 工具文件
- 🔧 `proxy_manager.py` - 代理管理工具
- 📖 `example_usage.py` - 使用示例
- 📚 `PROXY_ROTATION_GUIDE.md` - 详细使用指南

## 🚀 部署步骤

### 步骤1: 上传文件

1. **上传主要文件到项目目录**:
   ```
   Y:\**************\amaon\ifyapi\
   ├── amazon_scraper.py          # 已更新的主脚本
   ├── 代理IP.txt                 # 代理列表
   ├── check_environment.py       # 环境检查
   ├── simple_api_test.py         # 简单测试
   └── test_api_proxy.py          # 完整测试
   ```

2. **确保代理文件格式正确**:
   ```
   # 代理IP.txt 格式示例
   **************:9577
   ************:6047
   *************:5616
   ...
   ```

### 步骤2: 宝塔面板配置

1. **进入Python项目管理**
   - 登录宝塔面板
   - 进入"软件商店" → "Python项目管理"
   - 找到对应项目

2. **检查Python版本**
   - 确保Python版本 ≥ 3.7
   - 推荐使用Python 3.8+

3. **安装依赖模块**
   ```bash
   pip install requests flask beautifulsoup4
   ```

4. **启动项目**
   - 在宝塔面板中启动Python项目
   - 确认端口1122正常监听

### 步骤3: 环境检查

在项目目录中运行环境检查脚本：

```bash
cd /path/to/project
python check_environment.py
```

**预期输出**:
```
🔍 宝塔面板Python项目环境检查
========================================
🐍 Python环境检查
✅ Python版本符合要求 (>=3.7)

📦 Python模块检查
✅ requests
✅ flask
✅ bs4
✅ json
✅ threading
✅ collections
✅ concurrent.futures

📁 项目文件检查
✅ amazon_scraper.py (xxx bytes)
✅ 代理IP.txt (xxx bytes)

🌐 代理配置检查
✅ 找到 250 个有效代理

🔧 代理集成检查
✅ ProxyRotator类
✅ 代理轮换方法
✅ 代理参数
✅ 代理导入
✅ 线程支持
✅ API路由

🌐 API服务检查
✅ 服务正常

📈 总体结果: 6/6 检查通过
🎉 环境配置完整！可以开始测试代理功能
```

### 步骤4: 功能测试

#### 4.1 快速测试

```bash
python simple_api_test.py
```

#### 4.2 完整测试

```bash
python test_api_proxy.py
```

#### 4.3 手动API测试

使用curl命令测试：

```bash
# 测试不使用代理
curl -X POST http://**************:1122/api/scrape \
  -H "Content-Type: application/json" \
  -d '{
    "pageContent": "<html><div id=\"productTitle\">Test Product</div><div class=\"a-price\"><span class=\"a-offscreen\">€29.99</span></div></html>",
    "enable_variants": false,
    "warehouse_id": "3079",
    "send_to_api": false,
    "verbose_debug": true
  }'

# 测试使用代理轮换
curl -X POST http://**************:1122/api/scrape \
  -H "Content-Type: application/json" \
  -d '{
    "pageContent": "<html><div id=\"productTitle\">Test Product</div><div class=\"a-price\"><span class=\"a-offscreen\">€29.99</span></div><div id=\"twister\"><script>var twisterData={\"dimensionValuesData\":{\"B001\":{\"price\":\"29.99\"},\"B002\":{\"price\":\"32.99\"}}};</script></div></html>",
    "enable_variants": true,
    "warehouse_id": "3079", 
    "send_to_api": false,
    "verbose_debug": true
  }'
```

## 🔧 配置说明

### 代理轮换触发条件

代理轮换功能在以下情况下自动启用：

1. **API参数**: `enable_variants: true`
2. **存在变体**: HTML中包含多变体产品信息
3. **代理文件**: `代理IP.txt` 文件存在且包含有效代理

### 代理认证配置

在 `amazon_scraper.py` 中的配置：

```python
proxy_rotator = ProxyRotator(
    proxy_file="代理IP.txt",
    username="xinyc1126",      # 代理用户名
    password="woaiwojia001",   # 代理密码
    verbose=verbose_debug
)
```

### API请求参数

| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| `pageContent` | string | HTML内容 | 必需 |
| `enable_variants` | boolean | 是否启用变体（代理） | false |
| `warehouse_id` | string | 仓库ID | "3079" |
| `send_to_api` | boolean | 是否发送到WMS API | true |
| `verbose_debug` | boolean | 详细调试信息 | false |

## 📊 监控和日志

### 1. 代理使用统计

在API响应中查看代理统计信息：

```json
{
  "success": true,
  "data": {...},
  "debug_info": "代理轮换器初始化完成: {'total_proxies': 250, 'available_proxies': 248, 'failed_proxies': 2}"
}
```

### 2. 日志关键词

在 `verbose_debug: true` 模式下，查找以下关键词：

- `代理轮换器初始化完成` - 代理系统启动
- `切换到代理` - 代理轮换活动
- `标记代理失败` - 代理失效
- `剩余可用代理` - 代理池状态

### 3. 宝塔面板日志

在宝塔面板的Python项目日志中查看：
- 项目启动日志
- 错误日志
- 访问日志

## ⚠️ 故障排除

### 常见问题

#### 1. 代理文件问题
**症状**: `❌ 代理文件不存在: 代理IP.txt`
**解决**: 
- 确保文件名正确（包含中文字符）
- 检查文件编码为UTF-8
- 验证文件路径

#### 2. 代理格式错误
**症状**: `❌ 没有找到有效代理`
**解决**:
- 检查格式：每行 `IP:PORT`
- 移除空行和注释
- 验证IP和端口有效性

#### 3. API连接失败
**症状**: `❌ API连接失败`
**解决**:
- 检查宝塔面板项目状态
- 确认端口1122开放
- 重启Python项目

#### 4. 代理连接超时
**症状**: 请求超时或代理验证失败
**解决**:
- 测试代理可用性：`python proxy_manager.py --test`
- 更新代理列表
- 调整超时设置

### 调试步骤

1. **环境检查**:
   ```bash
   python check_environment.py
   ```

2. **代理测试**:
   ```bash
   python proxy_manager.py --test --workers 5
   ```

3. **API测试**:
   ```bash
   python simple_api_test.py
   ```

4. **查看日志**:
   - 宝塔面板项目日志
   - API响应中的debug_info

## 🎯 性能优化

### 1. 代理池管理
- 定期清理失效代理
- 添加高质量代理
- 监控代理成功率

### 2. 请求优化
- 合理设置超时时间
- 控制并发请求数量
- 实现请求重试机制

### 3. 缓存策略
- 缓存成功的代理
- 避免重复验证
- 实现代理预热

## 📈 监控指标

### 关键指标
- 代理可用率
- 请求成功率
- 平均响应时间
- 变体价格获取成功率

### 告警设置
- 可用代理数量 < 10
- 请求成功率 < 80%
- 平均响应时间 > 30秒

## 🎉 部署完成

完成以上步骤后，您的Amazon爬虫API将具备以下功能：

✅ **自动代理轮换** - 多变体产品价格获取时自动使用代理
✅ **智能失败处理** - 代理失败时自动切换
✅ **实时监控** - 代理使用统计和状态监控
✅ **灵活配置** - 支持启用/禁用代理功能
✅ **完整日志** - 详细的调试和监控信息

现在您可以通过API接口 `http://**************:1122/api/scrape` 使用代理轮换功能来获取Amazon多变体产品的价格信息了！
